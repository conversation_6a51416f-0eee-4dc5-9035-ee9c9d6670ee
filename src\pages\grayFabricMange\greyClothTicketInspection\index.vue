<script lang="ts" setup name="GreyClothTicketInspection">
import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Calendar, Check, CircleCheck, Connection, DataAnalysis, DataLine, Document, List, Monitor, Odometer, RefreshLeft, Search, Setting, TrendCharts, User, View } from '@element-plus/icons-vue'
import DefectInfoDialog from './components/DefectInfoDialog.vue'
import DefectRecordsDialog from './components/DefectRecordsDialog.vue'
import ElectronicScaleSettings from '@/components/ElectronicScaleSettings/index.vue'
import { getInfoBasicDefectlistEnum } from '@/api/fpQualityCheck'
import { GetFineCodeDetail } from '@/api/clothTicket'
import { GetDictionaryDetailEnumListApi } from '@/api/employees'
import { useMeasureWeightStore } from '@/stores/measureWeight'
import { BatchUpdateGlobalConfigList, GetGlobalConfigDropdownList } from '@/api/globalConfig'
import { Carry, CarryLabels, DecimalPoint, DecimalPointLabels, DictionaryType, EmployeeType, GlobalEnum } from '@/common/enum'
import SelectMergeComponent from '@/components/SelectMergeComponent/index.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { formatHashTag, formatWeightDiv } from '@/common/format'
import { useMeasureWeightService } from '@/use/useMeasureWeightService'
import { readUpperNumberCommand, useMeasureMeterService } from '@/use/useMeasurMeterService'
import { useMeasureMeterStore } from '@/stores/measureMeter'
import { Business_unitcustomerDetail } from '@/api/customerMange'
import { getProductionNotifyOrder } from '@/api/productionNotice'

// 表单数据
const formData = reactive({
  grade: '', // 等级
  barcode: '', // 条码
  machineNo: '', // 机号
  weaverId: undefined as number | undefined, // 织工ID
  weaverName: '', // 织工姓名
  inspectorId: undefined as number | undefined, // 查布ID
  inspectorName: '', // 查布姓名
  weighing: 0, // 称重
  actualWeight: 0, // 实重
  fineCodeId: null as number | null, // 细码ID
})

// 显示信息
const displayInfo = reactive({
  fabricName: '', // 坯布名称
  productionNotice: '', // 生产通知单
  rollNo: '', // 卷号
  productionOrder: '', // 排产单号
  dailyOutput: '', // 本日产量
  monthlyOutput: '', // 本月产量
  yarnName: '', // 纱名
  weightOfFabric: '', // 定重
})

// 加载状态
const loading = ref(false)

// 电子秤相关
const measureWeightStore = useMeasureWeightStore()
const isWeightConnected = computed(() => {
  return measureWeightStore.measureWeightState.isConnected
})
const WeightLog = computed(() => {
  return measureWeightStore.measureWeightState.Log
})
const showWeightLog = ref(false)
function toggleWeightLog() {
  showWeightLog.value = !showWeightLog.value
}

// 码表相关
const measureMeterStore = useMeasureMeterStore()
const isConnected = computed(() => {
  return measureMeterStore.measureMeterState.isConnected
})
const Log = computed(() => {
  return measureMeterStore.measureMeterState.Log
})
const showLog = ref(false)
function toggleLog() {
  showLog.value = !showLog.value
}
// 弹窗控制
const showElectronicScaleModal = ref(false)

// 疵点信息弹框引用
const defectInfoDialogRef = ref()

// 疵点记录弹框引用
const defectRecordsDialogRef = ref()

// 电子秤设置
const electronicScaleSettings = reactive({
  connection: '链接',
  stableValue: 15,
  isStable: false,
  autoSave: true,
  autoSaveSeconds: 3,
  noWeaverSelection: false,
  noInspectorSelection: false,
  weightReflection: false,
  dataHead: '',
  dataEnd: '',
  scanCodeReading: false,
  portAutoOpen: false,
  dataLength: '',
})

// 监听电子秤稳定数据
watch(() => measureWeightStore.measureWeightState.currentFrameData, (newValue: number) => {
  // 检查是否开启了"扫码后才读数"功能
  if (electronicScaleSettings.scanCodeReading) {
    // 如果开启了扫码后才读数，但没有扫码，则重量为0
    if (!formData.barcode || formData.barcode.trim() === '') {
      formData.weighing = 0
      return
    }
  }

  // 处理重量反转功能
  let processedWeight = newValue
  if (electronicScaleSettings.weightReflection)
    processedWeight = reverseWeight(newValue) // 重量反转：数值倒序读取

  // 更新重量
  formData.weighing = processedWeight
})

// 监听电子秤实时数据
watch(() => measureWeightStore.measureWeightState.currentWeight, (newValue: string) => {
  // 检查是否开启了"扫码后才读数"功能
  if (electronicScaleSettings.scanCodeReading) {
    // 如果开启了扫码后才读数，但没有扫码，则重量为0
    if (!formData.barcode || formData.barcode.trim() === '') {
      formData.actualWeight = 0
      return
    }
  }
  // 处理重量反转功能
  let processedWeight = Number(newValue)
  if (electronicScaleSettings.weightReflection)
    processedWeight = reverseWeight(Number(newValue)) // 重量反转：数值倒序读取

  // 更新重量
  formData.actualWeight = processedWeight
})

// 小数位数和进位方式配置
const weightPrecision = ref<DecimalPoint>(DecimalPoint.DecimalPointTwo) // 默认2位小数
const weightCarryType = ref<Carry>(Carry.CarryRoundOff) // 默认四舍五入
const fabricMaxWeight = ref<number>(0) // 坯布重量限制最大
const fabricMinWeight = ref<number>(0) // 坯布重量限制最小
// 全局配置相关
const { fetchData: getGlobalConfig, data: globalConfigData, success: globalConfigSuccess, msg: globalConfigMsg } = GetGlobalConfigDropdownList()
const { fetchData: saveConfig } = BatchUpdateGlobalConfigList()

// 客户详情相关
const { fetchData: fetchCustomerDetail, data: customerData, success: customerSuccess, msg: customerMsg } = Business_unitcustomerDetail()

// 细码详情相关
const { fetchData: fetchFineCodeDetail, success: fineCodeSuccess, msg: fineCodeMsg, data: fineCodeData } = GetFineCodeDetail()

// 生产通知单相关
const { fetchData: fetchProductionNotifyOrder, data: productionNotifyData, success: productionNotifySuccess, msg: productionNotifyMsg } = getProductionNotifyOrder()

// 配置ID映射 - 使用enum
const configIdMap = {
  autoSaveSeconds: GlobalEnum.AutoSaveSeconds, // 设置延后多少秒保存
  noWeaverSelection: GlobalEnum.NoWeaverSelection, // 是否无需选择织工
  noInspectorSelection: GlobalEnum.NoInspectorSelection, // 是否无需选择查布
  weightReflection: GlobalEnum.WeightReflection, // 是否重量反转
  dataHead: GlobalEnum.DataHead, // 数据头 文本
  dataEnd: GlobalEnum.DataEnd, // 数据尾 文本
  scanCodeReading: GlobalEnum.ScanCodeReading, // 是否扫码后读数
  stableValue: GlobalEnum.StableValue, // 设置稳定值 文本
  autoSave: GlobalEnum.AutoSave, // 是否自动保存
}

// 获取设置数据
async function getSettingsData() {
  const ids = Object.values(configIdMap).join(',')
  await getGlobalConfig({
    ids,
  })

  // 将获取到的配置应用到设置中
  if (globalConfigSuccess.value)
    applyGlobalConfigToSettings()
  else
    ElMessage.error(globalConfigMsg.value)
}

// 将全局配置应用到电子秤设置
function applyGlobalConfigToSettings() {
  if (!globalConfigData.value)
    return

  globalConfigData.value?.list?.forEach((config: any) => {
    const configId = config.id
    switch (configId) {
      case configIdMap.autoSaveSeconds:
        electronicScaleSettings.autoSaveSeconds = Number(config.options) || 3
        break
      case configIdMap.noWeaverSelection:
        electronicScaleSettings.noWeaverSelection = config.options === 'true' || config.options === true
        break
      case configIdMap.noInspectorSelection:
        electronicScaleSettings.noInspectorSelection = config.options === 'true' || config.options === true
        break
      case configIdMap.weightReflection:
        electronicScaleSettings.weightReflection = config.options === 'true' || config.options === true
        break
      case configIdMap.dataHead:
        electronicScaleSettings.dataHead = config.options || ''
        break
      case configIdMap.dataEnd:
        electronicScaleSettings.dataEnd = config.options || ''
        break
      case configIdMap.scanCodeReading:
        electronicScaleSettings.scanCodeReading = config.options === 'true' || config.options === true
        break
      case configIdMap.stableValue:
        electronicScaleSettings.stableValue = Number(config.options) || 0
        electronicScaleSettings.isStable = !!Number(config.options)
        break
      case configIdMap.autoSave:
        electronicScaleSettings.autoSave = config.options === 'true' || config.options === true
        break
    }
  })

  // 应用配置到 measureWeightStore
  updateWeightConfig()
}

// 更新电子秤配置到 measureWeightStore
function updateWeightConfig() {
  // 更新数据头尾配置
  const delimiterConfig = {
    dataHeader: electronicScaleSettings.dataHead
      ? Array.from(electronicScaleSettings.dataHead).map(char => char.charCodeAt(0))
      : [],
    dataFooter: electronicScaleSettings.dataEnd
      ? Array.from(electronicScaleSettings.dataEnd).map(char => char.charCodeAt(0))
      : [13, 10], // 默认 \r\n
    useCustomDelimiter: !!(electronicScaleSettings.dataHead || electronicScaleSettings.dataEnd),
  }

  // 更新稳定性配置
  const stabilityConfig = {
    enabled: electronicScaleSettings.isStable,
    stableCount: Number(electronicScaleSettings.stableValue) || 3,
    precision: weightPrecision.value, // 使用配置的精度
  }

  // 更新现有连接的配置
  if (measureWeightStore.measureWeightState.updateDelimiterConfig)
    measureWeightStore.measureWeightState.updateDelimiterConfig(delimiterConfig)

  if (measureWeightStore.measureWeightState.updateStabilityConfig)
    measureWeightStore.measureWeightState.updateStabilityConfig(stabilityConfig)
}

// 保存设置到全局配置
async function saveSettingsToGlobalConfig() {
  const configsToSave = [
    { id: configIdMap.autoSaveSeconds, options: electronicScaleSettings.autoSaveSeconds.toString() },
    { id: configIdMap.noWeaverSelection, options: electronicScaleSettings.noWeaverSelection.toString() },
    { id: configIdMap.noInspectorSelection, options: electronicScaleSettings.noInspectorSelection.toString() },
    { id: configIdMap.weightReflection, options: electronicScaleSettings.weightReflection.toString() },
    { id: configIdMap.dataHead, options: electronicScaleSettings.dataHead },
    { id: configIdMap.dataEnd, options: electronicScaleSettings.dataEnd },
    { id: configIdMap.scanCodeReading, options: electronicScaleSettings.scanCodeReading.toString() },
    { id: configIdMap.stableValue, options: electronicScaleSettings.stableValue.toString() },
    { id: configIdMap.autoSave, options: electronicScaleSettings.autoSave.toString() },
  ]

  try {
    await saveConfig({
      update_global_config_list: configsToSave.map((item) => {
        return {
          id: item.id,
          options: item.options,
        }
      }),
    })
    ElMessage.success('设置保存成功')
  }
  catch (error) {
    ElMessage.error('设置保存失败')
    console.error('保存设置失败:', error)
  }
}

// 应用进位方式
function applyCarryType(value: number, carryType: Carry, precision: number): number {
  const multiplier = 10 ** precision

  switch (carryType) {
    case Carry.CarryOne:
      // 逢1进位：小数部分有任何值就进位
      return Math.ceil(value * multiplier) / multiplier
    case Carry.CarryTwo:
      // 逢2进位：小数部分>=0.2就进位
      return Math.ceil((value - 0.1) * multiplier) / multiplier
    case Carry.CarryThree:
      // 逢3进位：小数部分>=0.3就进位
      return Math.ceil((value - 0.2) * multiplier) / multiplier
    case Carry.CarryFour:
      // 逢4进位：小数部分>=0.4就进位
      return Math.ceil((value - 0.3) * multiplier) / multiplier
    case Carry.CarryRoundOff:
    default:
      // 四舍五入
      return Math.round(value * multiplier) / multiplier
  }
}

// 重量反转函数 - 数值倒序读取
function reverseWeight(weight: number): number {
  if (weight === 0)
    return 0

  const isNegative = weight < 0
  const absWeight = Math.abs(weight)

  // 获取当前的小数位数设置
  const decimalPlaces = weightPrecision.value

  // 转换为字符串，使用动态小数位数
  const weightStr = absWeight.toFixed(decimalPlaces)

  // 移除小数点，获取所有数字
  const digitsOnly = weightStr.replace('.', '')

  // 倒序排列数字
  const reversedDigits = digitsOnly.split('').reverse().join('')

  // 重新插入小数点
  const reversedWeight = Number.parseFloat(
    `${reversedDigits.slice(0, -decimalPlaces)}.${reversedDigits.slice(-decimalPlaces)}`,
  )

  // 应用进位方式
  const processedWeight = applyCarryType(reversedWeight, weightCarryType.value, decimalPlaces)

  // 恢复符号
  return isNegative ? -processedWeight : processedWeight
}

// 获取细码详情
async function getFineCodeDetail(barcode?: string, id?: number) {
  if (!barcode && !id) {
    ElMessage.error('请提供条码或细码ID')
    return
  }

  loading.value = true
  try {
    await fetchFineCodeDetail({
      fabric_piece_code: barcode,
      id,
    })

    if (fineCodeSuccess.value) {
      // 更新表单数据
      formData.fineCodeId = fineCodeData.value.id || null
      formData.machineNo = fineCodeData.value.machine_number || ''

      // 更新显示信息
      displayInfo.fabricName = `${fineCodeData.value.grey_fabric_code || ''}#${fineCodeData.value.grey_fabric_name || ''}`
      displayInfo.productionNotice = fineCodeData.value.production_notify_order_no || ''
      displayInfo.rollNo = fineCodeData.value.volume_number?.toString() || ''
      displayInfo.productionOrder = fineCodeData.value.production_schedule_order_no || ''
      displayInfo.dailyOutput = `${fineCodeData.value.today_weighing_count}条`
      displayInfo.monthlyOutput = `${fineCodeData.value.month_weighing_count}条`
      displayInfo.yarnName = fineCodeData.value.yarn_batch || ''
      displayInfo.weightOfFabric = fineCodeData.value.weight_of_fabric ? `${formatWeightDiv(fineCodeData.value.weight_of_fabric || 0)}kg` : ''
      const carry = fineCodeData.value.carry as Carry
      const decimalPoint = fineCodeData.value.decimal_point as DecimalPoint

      // 更新小数位数和进位方式配置
      if (decimalPoint !== undefined && decimalPoint !== null)
        weightPrecision.value = decimalPoint

      if (carry !== undefined && carry !== null)
        weightCarryType.value = carry
      fabricMaxWeight.value = formatWeightDiv(fineCodeData.value.fabric_max_weight || 0)
      fabricMinWeight.value = formatWeightDiv(fineCodeData.value.fabric_min_weight || 0)
    }
    else {
      ElMessage.error(fineCodeMsg.value || '获取细码详情失败')
    }
  }
  catch (error: any) {
    ElMessage.error(error.message || '获取细码详情失败')
  }
  finally {
    loading.value = false
  }
}

// 扫码输入处理
async function handleBarcodeInput() {
  if (formData.barcode) {
    await getFineCodeDetail(formData.barcode)

    // 如果开启了"扫码后才读数"功能，扫码后立即更新电子秤重量
    if (electronicScaleSettings.scanCodeReading && measureWeightStore.measureWeightState.currentFrameData) {
      let currentFrameData = measureWeightStore.measureWeightState.currentFrameData
      let currentWeight = Number(measureWeightStore.measureWeightState.currentWeight)

      // 处理重量反转功能
      if (electronicScaleSettings.weightReflection) {
        currentFrameData = reverseWeight(currentFrameData) // 重量反转：数值倒序读取
        currentWeight = reverseWeight(currentWeight) // 重量反转：数值倒序读取
      }

      formData.weighing = currentFrameData
      formData.actualWeight = currentWeight
    }
  }
  else {
    // 如果清空了条码，清空相关信息
    formData.fineCodeId = null
    formData.machineNo = ''
    formData.weaverId = undefined
    formData.weaverName = ''
    formData.inspectorId = undefined
    formData.inspectorName = ''
    displayInfo.fabricName = ''
    displayInfo.productionNotice = ''
    displayInfo.rollNo = ''
    displayInfo.productionOrder = ''
    displayInfo.dailyOutput = ''
    displayInfo.monthlyOutput = ''
    displayInfo.yarnName = ''

    // 如果开启了"扫码后才读数"功能，清空条码后重量归零
    if (electronicScaleSettings.scanCodeReading) {
      formData.weighing = 0
      formData.actualWeight = 0
    }
  }
}

const ruleFormRef = ref<FormInstance>()
const rules = computed(() => ({
  barcode: [
    { required: true, message: '请输入条码', trigger: 'change' },
  ],
  weaverId: [
    { required: !electronicScaleSettings.noWeaverSelection, message: '请选择织工', trigger: 'blur' },
  ],
  inspectorId: [
    { required: !electronicScaleSettings.noInspectorSelection, message: '请选择查布', trigger: 'blur' },
  ],
}))
// 连接串口设备
function handleConnectToSerialPort() {
  if (!isWeightConnected.value) {
    // 准备数据头尾配置
    const delimiterConfig = {
      dataHeader: electronicScaleSettings.dataHead
        ? Array.from(electronicScaleSettings.dataHead).map(char => char.charCodeAt(0))
        : [],
      dataFooter: electronicScaleSettings.dataEnd
        ? Array.from(electronicScaleSettings.dataEnd).map(char => char.charCodeAt(0))
        : [13, 10], // 默认 \r\n
      useCustomDelimiter: !!(electronicScaleSettings.dataHead || electronicScaleSettings.dataEnd),
    }

    // 准备稳定性配置
    const stabilityConfig = {
      enabled: electronicScaleSettings.isStable,
      stableCount: electronicScaleSettings.stableValue || 3,
      precision: weightPrecision.value, // 使用配置的精度
    }

    const measureWeightService = useMeasureWeightService(delimiterConfig, stabilityConfig)
    try {
      measureWeightService.connectToSerialPort()
      measureWeightStore.setMeasureWeightState(measureWeightService)
    }
    catch (error) {
      console.error('串口连接错误:', error)
    }
  }
}

// 断开电子秤串口连接
function handleDisconnectToWeightSerialPort() {
  measureWeightStore.measureWeightState.clearLogMessages()
  measureWeightStore.clearLogList()
  measureWeightStore.measureWeightState.disconnectPort()
}

// 连接码表串口设备
function handleConnectToMeterSerialPort() {
  if (!isConnected.value) {
    const measureMeterService = useMeasureMeterService()
    try {
      measureMeterService.connectToSerialPort()
      measureMeterStore.setMeasureMeterState(measureMeterService)
    }
    catch (error) {
      console.error('码表串口连接错误:', error)
    }
  }
}

// 断开码表串口连接
function handleDisconnectToSerialPort() {
  measureMeterStore.measureMeterState.clearLogMessages()
  measureMeterStore.clearLogList()
  measureMeterStore.measureMeterState.disconnectPort()
}

// 码表清零
function handleZero() {
  measureMeterStore.measureMeterState.handleZero()
}

// 监听码表数据变化
const currentFrameData = computed(() => measureMeterStore.measureMeterState.currentFrameData)

// 监听码表数据变化，实时更新疵点位置
watch(() => currentFrameData.value, (newVal) => {
  // 如果疵点弹框是打开状态，实时更新疵点位置
  if (defectInfoDialogRef.value?.state.showModal && newVal)
    defectInfoDialogRef.value.setDefectPosition(newVal)
})

// 自动保存 - 移除独立的autoSave，使用electronicScaleSettings.autoSave

// 当前选中的tab
const activeTab = ref('all')

// 疵点分类相关
const defectCategories = ref<any[]>([])

// 疵点API
const { fetchData: fetchList, data: resList, success: fetchSuccess, loading: defectTypesLoading } = getInfoBasicDefectlistEnum()

// 疵点分类API
const { fetchData: fetchDefectCategories, data: defectCategoriesData, success: defectCategoriesSuccess } = GetDictionaryDetailEnumListApi()

// 疵点数据 - 从API获取
const defectTypes = ref<Array<{ id?: number, name: string, count: number, kind_id?: number }>>([])

// 获取骨架屏按钮宽度
function getSkeletonWidth(index: number) {
  const widths = ['80px', '100px', '120px', '90px', '110px', '85px']
  return widths[index % widths.length]
}

// 加载疵点分类
async function loadDefectCategories() {
  try {
    await fetchDefectCategories({ dictionary_id: DictionaryType.defectKind })
    if (defectCategoriesSuccess.value) {
      const categories = defectCategoriesData.value?.list || []
      defectCategories.value = [
        { id: 'all', name: '全部疵点' },
        ...categories.map((item: any) => ({
          id: item.id,
          name: item.name,
        })),
      ]
    }
  }
  catch (error) {
    console.error('加载疵点分类失败:', error)
    // 如果加载失败，使用默认分类
    defectCategories.value = [
      { id: 'all', name: '全部疵点' },
      { id: 'pretreatment', name: '前处理' },
      { id: 'qualitative', name: '定性' },
      { id: 'weaving', name: '织造' },
    ]
  }
}

// 加载疵点列表
async function loadDefectTypes() {
  await fetchList()
  if (fetchSuccess.value) {
    const data = resList?.value.list || []
    // 将API数据转换为组件需要的格式，只取状态为启用的疵点
    const apiDefects = data
      .filter((item: Api.DefectBasicInfo.Response) => item.status === 1) // 只取启用状态的疵点
      .map((item: Api.DefectBasicInfo.Response) => ({
        ...item,
        id: item.id,
        name: item.name || '未知疵点',
        count: 0,
        kind_id: item.kind_id, // 添加疵点分类ID
      }))

    defectTypes.value = apiDefects

    // 确保"其他"选项存在
    const hasOther = defectTypes.value.some(item => item.name === '其他')
    if (!hasOther)
      defectTypes.value.push({ name: '其他', count: 0 })
  }
  else {
    throw new Error('API返回数据格式不正确')
  }
}

// 根据当前选中的分类过滤疵点
const filteredDefectTypes = computed(() => {
  if (activeTab.value === 'all')
    return defectTypes.value

  // 根据分类ID过滤疵点
  const categoryId = Number(activeTab.value)
  return defectTypes.value.filter(item => item.kind_id === categoryId)
})

// 计算总疵点数
const totalDefects = computed(() => {
  return defectTypes.value.reduce((total, item) => total + item.count, 0)
})

// 详细疵点统计数据 - 记录每个疵点的详细信息
const defectStatistics = ref<Array<{
  id: string
  name: string
  barcode: string
  position: number
  count: number
  score: number
  timestamp: string
}>>([])

// 计算疵点统计显示数据
const defectStatisticsDisplay = computed(() => {
  const stats = new Map<string, { count: number, totalScore: number }>()

  // 统计每种疵点的数量和总分数
  defectStatistics.value.forEach((item) => {
    const key = item.name
    if (stats.has(key)) {
      const existing = stats.get(key)!
      existing.count += item.count
      existing.totalScore += item.score * item.count
    }
    else {
      stats.set(key, { count: item.count, totalScore: item.score * item.count })
    }
  })

  // 转换为显示格式
  return Array.from(stats.entries()).map(([name, data]) => ({
    name,
    count: data.count,
    totalScore: data.totalScore,
    displayText: `${name}(${data.count})`,
  }))
})

// 保存
async function save() {
  if (!ruleFormRef.value)
    return

  try {
    await ruleFormRef.value.validate()

    if (!formData.fineCodeId) {
      ElMessage.error('请先扫码获取细码信息')
      return
    }

    if (!electronicScaleSettings.noWeaverSelection && !formData.weaverId) {
      ElMessage.error('请选择织工')
      return
    }

    if (!electronicScaleSettings.noInspectorSelection && !formData.inspectorId) {
      ElMessage.error('请选择查布')
      return
    }

    // TODO: 实现保存逻辑，调用验布相关的API
    ElMessage.success('保存成功')
  }
  catch (error) {
    ElMessage.error('请完善表单信息')
  }
}

// 清空
function clear() {
  ElMessageBox.confirm('确定要清空所有数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    Object.assign(formData, {
      grade: '',
      barcode: '',
      machineNo: '',
      weaverId: undefined,
      weaverName: '',
      inspectorId: undefined,
      inspectorName: '',
      weighing: 0,
      actualWeight: 0,
      fineCodeId: null,
    })

    // 重置显示信息
    Object.assign(displayInfo, {
      fabricName: 'xxx坯布编号#坯布名称',
      productionNotice: 'xxxxxxx',
      rollNo: '10',
      productionOrder: 'xxxxxxxxxxx',
      dailyOutput: '32条',
      monthlyOutput: '333条',
      yarnName: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
    })

    defectTypes.value.forEach(item => item.count = 0)
    defectStatistics.value = []
    ElMessage.success('清空成功')
  })
}

// 设置电子秤
function openElectronicScaleModal() {
  showElectronicScaleModal.value = true
}

async function saveElectronicScaleSettings(settings?: any) {
  // 如果传入了设置参数，更新本地设置
  if (settings)
    Object.assign(electronicScaleSettings, settings)

  // 保存到全局配置
  await saveSettingsToGlobalConfig()

  // 如果已经连接，更新现有连接的配置
  if (isWeightConnected.value && measureWeightStore.measureWeightState) {
    updateWeightConfig()
    ElMessage.success('电子秤设置已更新并应用到当前连接')
  }

  // 如果是从组件保存，不需要关闭弹窗（组件会自己关闭）
  if (!settings)
    showElectronicScaleModal.value = false

  ElMessage.success('电子秤设置保存成功')
}

// 点击疵点
function clickDefect(defect: any) {
  // 设置码表命令为读取上排数值
  measureMeterStore.setCommand(readUpperNumberCommand)
  // 开始码表计时
  if (isConnected.value)
    measureMeterStore.measureMeterState.resume()

  // 打开疵点信息弹框
  const isOther = defect.name === '其他'
  defectInfoDialogRef.value?.showAddDialog(defect, isOther, true)

  // 将当前码表数值设置到疵点位置
  if (defectInfoDialogRef.value && currentFrameData.value)
    defectInfoDialogRef.value.setDefectPosition(currentFrameData.value)
}

// 疵点弹框关闭时暂停码表
function handleDefectDialogClose() {
  // 暂停码表计时
  if (isConnected.value)
    measureMeterStore.measureMeterState.pause()
}

// 处理疵点信息确认
function handleDefectSure(defectData: any) {
  // 更新疵点数量
  const defectType = defectTypes.value.find(item => item.name === defectData.defect_name || item.name === defectData.name)
  if (defectType)
    defectType.count += defectData.defect_count

  // 添加到详细统计数据
  const statisticItem = {
    id: `defect_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    name: defectData.defect_name || defectData.name,
    barcode: formData.barcode || '000111782349', // 从表单获取条形码
    position: defectData.defect_position || 0,
    count: defectData.defect_count,
    score: defectData.score,
    timestamp: new Date().toLocaleString(),
  }
  defectStatistics.value.push(statisticItem)

  ElMessage.success('疵点信息添加成功')
}

// 点击疵点统计项
function handleStatItemClick(defectName: string) {
  if (defectName === 'all') {
    // 显示疵点记录弹框
    defectRecordsDialogRef.value?.showDialog('', defectStatistics.value)
    return
  }
  // 过滤出该疵点类型的所有记录
  const records = defectStatistics.value.filter(record => record.name === defectName)
  if (records.length === 0) {
    ElMessage.info(`暂无${defectName}的记录`)
    return
  }

  // 显示疵点记录弹框
  defectRecordsDialogRef.value?.showDialog(defectName, defectStatistics.value)
}

// 处理疵点记录更新
function handleDefectRecordUpdate(editedData: any) {
  // 更新详细统计数据中的记录
  const index = defectStatistics.value.findIndex(item => item.id === editedData.originalId)
  if (index > -1) {
    const oldRecord = defectStatistics.value[index]

    // 更新记录
    defectStatistics.value[index] = {
      ...oldRecord,
      name: editedData.defect_name || editedData.name,
      position: editedData.defect_position || 0,
      count: editedData.defect_count,
      score: editedData.score,
      timestamp: new Date().toLocaleString(), // 更新时间戳
    }

    // 如果疵点名称发生变化，需要更新疵点类型计数
    if (oldRecord.name !== editedData.defect_name) {
      // 从旧疵点类型中减去数量
      const oldDefectType = defectTypes.value.find(item => item.name === oldRecord.name)
      if (oldDefectType && oldDefectType.count >= oldRecord.count)
        oldDefectType.count -= oldRecord.count

      // 向新疵点类型中添加数量
      const newDefectType = defectTypes.value.find(item => item.name === editedData.defect_name)
      if (newDefectType)
        newDefectType.count += editedData.defect_count
    }
    else {
      // 疵点名称未变化，只更新数量差异
      const defectType = defectTypes.value.find(item => item.name === oldRecord.name)
      if (defectType) {
        const countDiff = editedData.defect_count - oldRecord.count
        defectType.count += countDiff
        // 确保计数不为负数
        if (defectType.count < 0)
          defectType.count = 0
      }
    }

    ElMessage.success('疵点记录更新成功')
  }
}

// 处理疵点记录删除
function handleDefectRecordDelete(record: any) {
  // 从详细统计数据中移除
  const index = defectStatistics.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    const deletedRecord = defectStatistics.value[index]
    defectStatistics.value.splice(index, 1)

    // 更新疵点类型计数
    const defectType = defectTypes.value.find(item => item.name === deletedRecord.name)
    if (defectType && defectType.count >= deletedRecord.count)
      defectType.count -= deletedRecord.count
  }
}

// 键盘事件
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'F4') {
    event.preventDefault()
    save()
  }
  else if (event.key === 'F1') {
    event.preventDefault()
    clear()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  // 加载疵点分类
  loadDefectCategories()
  // 加载疵点列表
  loadDefectTypes()
  // 加载电子秤设置
  getSettingsData()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <div class="grey-cloth-inspection">
    <el-row :gutter="10" class="main-content">
      <!-- 左侧表单区域 -->
      <el-col :span="12" class="left-panel">
        <el-card class="form-section" body-class="h-full flex flex-col">
          <div class="flex-1 overflow-y-scroll overflow-x-hidden mb-2">
            <h3 class="section-title">
              质检查布
            </h3>

            <!-- 表单区域 -->
            <el-form ref="ruleFormRef" :inline="true" :rules="rules" size="large" :model="formData" label-width="80px" class="inspection-form">
              <!-- 基础信息行 -->
              <div class="form-section-header">
                <span class="section-icon">📋</span>
                <span class="section-text">基础信息</span>
              </div>

              <el-row :gutter="16" class="form-row">
                <el-col :span="10">
                  <el-form-item label="条码:" size="large" prop="barcode" class="barcode-item">
                    <el-input
                      v-model="formData.barcode"
                      placeholder="请输入条码"
                      class="barcode-input"
                      @change="handleBarcodeInput"
                    >
                      <template #prefix>
                        <el-icon><DataLine /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="等级:" class="grade-item">
                    <SelectComponents
                      v-model="formData.grade"
                      style="width: 150px"
                      api="GetDictionaryDetailEnumListApi"
                      placeholder="请选择"
                      label-field="name"
                      value-field="id"
                      :query="{ dictionary_id: DictionaryType.clothLevel }"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="机号:" class="machine-item">
                    <el-tooltip
                      :content="formData.machineNo ? `当前机号: ${formData.machineNo}` : '请先扫码获取机号信息'"
                      placement="top"
                    >
                      <div class="machine-display" :class="{ 'has-machine': formData.machineNo }">
                        <el-icon class="machine-icon">
                          <Monitor />
                        </el-icon>
                        <span class="machine-text">{{ formData.machineNo }}</span>
                        <el-icon v-if="formData.machineNo" class="status-icon">
                          <CircleCheck />
                        </el-icon>
                      </div>
                    </el-tooltip>
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 人员信息行 -->
              <div class="form-section-header">
                <span class="section-icon">👥</span>
                <span class="section-text">人员信息</span>
              </div>

              <el-row :gutter="16" class="form-row">
                <el-col :span="12">
                  <el-form-item label="织工:" size="large" prop="weaverId" :required="!electronicScaleSettings.noWeaverSelection">
                    <div class="worker-selector">
                      <SelectMergeComponent
                        v-model="formData.weaverId"
                        :custom-label="(row: any) => `${formatHashTag(row.code, row.name)}`"
                        :query="{ duty: EmployeeType.follower }"
                        api-name="Adminemployeelist"
                        placeholder="请选择织工"
                        remote
                        remote-key="code_or_name"
                        remote-show-suffix
                        label-field="name"
                        value-field="id"
                        clearable
                        class="worker-input"
                        @change="(item: any) => formData.weaverName = item.name"
                      >
                        <template #prefix>
                          <el-icon><User /></el-icon>
                        </template>
                      </SelectMergeComponent>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="查布:" prop="inspectorId" :required="!electronicScaleSettings.noInspectorSelection">
                    <div class="worker-selector">
                      <SelectMergeComponent
                        v-model="formData.inspectorId"
                        :custom-label="(row: any) => `${formatHashTag(row.code, row.name)}`"
                        :query="{ duty: EmployeeType.inspector }"
                        api-name="Adminemployeelist"
                        placeholder="请选择查布"
                        remote
                        remote-key="code_or_name"
                        remote-show-suffix
                        label-field="name"
                        value-field="id"
                        clearable
                        class="worker-input"
                        @change="(item: any) => formData.inspectorName = item.name"
                      >
                        <template #prefix>
                          <el-icon><View /></el-icon>
                        </template>
                      </SelectMergeComponent>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 重量信息行 -->
              <div class="form-section-header">
                <span class="section-icon">⚖️</span>
                <h4 class="section-text">
                  重量信息
                  <el-tag type="primary" size="small" class="ml-2" effect="light">
                    保留{{ DecimalPointLabels[weightPrecision] }}
                  </el-tag>
                  <el-tag type="success" size="small" class="ml-2" effect="light">
                    {{ CarryLabels[weightCarryType] }}
                  </el-tag>
                  <el-tag v-if="fabricMinWeight" type="success" size="small" class="weight-limit-tag ml-2">
                    最小 {{ fabricMinWeight }}kg
                  </el-tag>
                  <el-tag v-if="fabricMaxWeight" type="danger" size="small" class="weight-limit-tag ml-2">
                    最大 {{ fabricMaxWeight }}kg
                  </el-tag>
                </h4>
              </div>

              <el-row :gutter="16" class="form-row weight-row">
                <el-col :span="12">
                  <el-form-item class="weight-item">
                    <template #label>
                      <div class="weight-label">
                        <span>称重:</span>
                      </div>
                    </template>
                    <el-input-number
                      v-model="formData.weighing"
                      :precision="weightPrecision"
                      :step="0.01"
                      style="width: 100%"
                      placeholder="称重"
                      class="weight-input"
                    >
                      <template #suffix>
                        <span class="weight-unit">kg</span>
                      </template>
                    </el-input-number>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="实重:" class="weight-item">
                    <el-input-number
                      v-model="formData.actualWeight"
                      :precision="weightPrecision"
                      :step="0.01"
                      style="width: 100%"
                      placeholder="实重"
                      class="weight-input"
                    >
                      <template #suffix>
                        <span class="weight-unit">kg</span>
                      </template>
                    </el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>

            <!-- 显示信息区域 -->
            <div class="info-display">
              <div class="info-section-header">
                <span class="section-icon">📊</span>
                <span class="section-text">生产信息</span>
              </div>

              <div class="info-grid">
                <div class="info-card">
                  <div class="info-card-header">
                    <el-icon class="info-icon">
                      <Document />
                    </el-icon>
                    <span class="info-label">坯布名称</span>
                  </div>
                  <div class="info-value">
                    {{ displayInfo.fabricName }}
                  </div>
                </div>

                <div class="info-card">
                  <div class="info-card-header">
                    <el-icon class="info-icon">
                      <List />
                    </el-icon>
                    <span class="info-label">生产通知单</span>
                  </div>
                  <div class="info-value">
                    {{ displayInfo.productionNotice }}
                  </div>
                </div>

                <div class="info-card">
                  <div class="info-card-header">
                    <el-icon class="info-icon">
                      <CircleCheck />
                    </el-icon>
                    <span class="info-label">卷号</span>
                  </div>
                  <div class="info-value">
                    {{ displayInfo.rollNo }}
                  </div>
                </div>

                <div class="info-card">
                  <div class="info-card-header">
                    <el-icon class="info-icon">
                      <Calendar />
                    </el-icon>
                    <span class="info-label">排产单号</span>
                  </div>
                  <div class="info-value">
                    {{ displayInfo.productionOrder }}
                  </div>
                </div>

                <div class="info-card">
                  <div class="info-card-header">
                    <el-icon class="info-icon">
                      <TrendCharts />
                    </el-icon>
                    <span class="info-label">本日产量</span>
                  </div>
                  <div class="info-value highlight">
                    {{ displayInfo.dailyOutput }}
                  </div>
                </div>

                <div class="info-card">
                  <div class="info-card-header">
                    <el-icon class="info-icon">
                      <DataAnalysis />
                    </el-icon>
                    <span class="info-label">本月产量</span>
                  </div>
                  <div class="info-value highlight">
                    {{ displayInfo.monthlyOutput }}
                  </div>
                </div>
              </div>

              <div class="yarn-info-card">
                <div class="yarn-header">
                  <el-icon class="yarn-icon">
                    <Connection />
                  </el-icon>
                  <span class="yarn-label">纱名</span>
                </div>
                <div class="yarn-content">
                  {{ displayInfo.yarnName }}
                </div>
              </div>
            </div>
          </div>

          <!-- 底部操作区域 -->
          <div class="bottom-actions">
            <div class="weight-requirement">
              <el-icon class="requirement-icon">
                <Odometer />
              </el-icon>
              <span class="requirement-text">要求布重：</span>
              <span class="requirement-value">{{ displayInfo.weightOfFabric || '待获取' }}</span>
            </div>
            <div class="action-buttons">
              <div class="primary-actions">
                <el-button size="large" type="primary" class="save-btn" @click="save">
                  <el-icon><Check /></el-icon>
                  保存(F4)
                </el-button>
                <el-button size="large" class="clear-btn" @click="clear">
                  <el-icon><RefreshLeft /></el-icon>
                  清空(F1)
                </el-button>
              </div>
              <div class="secondary-actions">
                <el-checkbox v-model="electronicScaleSettings.autoSave" size="large" class="auto-save-checkbox">
                  <span class="checkbox-text">自动保存</span>
                </el-checkbox>
                <el-button size="large" class="settings-btn" @click="openElectronicScaleModal">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧疵点选择区域 -->
      <el-col :span="12" class="right-panel">
        <el-card class="defect-section" body-class="h-full flex flex-col">
          <el-tabs v-model="activeTab" size="large" class="defect-tabs" tab-position="top">
            <!-- 动态疵点分类 -->
            <el-tab-pane
              v-for="category in defectCategories"
              :key="category.id"
              :label="category.name"
              :name="category.id"
            >
              <div class="defect-buttons">
                <!-- 骨架屏 -->
                <el-skeleton :loading="defectTypesLoading">
                  <template #template>
                    <div class="defect-skeleton">
                      <el-skeleton-item
                        v-for="i in 16"
                        :key="i"
                        variant="button"
                        :style="{
                          width: getSkeletonWidth(i),
                          height: '40px',
                          marginRight: '10px',
                          marginBottom: '10px',
                        }"
                      />
                    </div>
                  </template>
                  <template #default>
                    <el-button
                      v-for="defect in filteredDefectTypes"
                      :key="defect.name"
                      class="defect-btn" :class="[{ selected: defect.count > 0 }]"
                      @click="clickDefect(defect)"
                    >
                      {{ defect.name }}
                      <span v-if="defect.count > 0" class="defect-count">({{ defect.count }})</span>
                    </el-button>
                  </template>
                </el-skeleton>
              </div>
            </el-tab-pane>
          </el-tabs>

          <!-- 疵点统计 -->
          <div class="defect-statistics">
            <el-scrollbar>
              <div class="statistics-content">
                <span class="stat-item" @click="handleStatItemClick('all')">总疵点 ({{ totalDefects }})</span>
                <!-- <span class="stat-item">疵点种类 ({{ selectedDefects }})</span> -->
                <span
                  v-for="item in defectStatisticsDisplay"
                  :key="item.name"
                  class="stat-item clickable"
                  @click="handleStatItemClick(item.name)"
                >
                  {{ item.displayText }}
                </span>
                <span v-if="defectStatisticsDisplay.length === 0" class="stat-item no-data">
                  暂无疵点记录
                </span>
              </div>
            </el-scrollbar>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设置电子秤弹窗 -->
    <ElectronicScaleSettings
      v-model="showElectronicScaleModal"
      :settings="electronicScaleSettings"
      :show-meter-connection="true"
      :show-inspector-option="true"
      :is-weight-connected="isWeightConnected"
      :is-meter-connected="isConnected"
      :show-weight-log="showWeightLog"
      :show-meter-log="showLog"
      @save="saveElectronicScaleSettings"
      @connect-weight="handleConnectToSerialPort"
      @disconnect-weight="handleDisconnectToWeightSerialPort"
      @connect-meter="handleConnectToMeterSerialPort"
      @disconnect-meter="handleDisconnectToSerialPort"
      @toggle-weight-log="toggleWeightLog"
      @toggle-meter-log="toggleLog"
      @meter-zero="handleZero"
    >
      <template #weight-log>
        <WeightLog v-if="showWeightLog" />
      </template>
      <template #meter-log>
        <Log v-if="showLog" />
      </template>
    </ElectronicScaleSettings>

    <!-- 疵点信息弹框 -->
    <DefectInfoDialog
      ref="defectInfoDialogRef"
      @handle-sure="handleDefectSure"
      @handle-hide="handleDefectDialogClose"
    />

    <!-- 疵点记录弹框 -->
    <DefectRecordsDialog
      ref="defectRecordsDialogRef"
      @on-update="handleDefectRecordUpdate"
      @on-delete="handleDefectRecordDelete"
    />
  </div>
</template>

<style lang="scss" scoped>
.grey-cloth-inspection {
  overflow: hidden;
  height: 100%;
  background: #f8fafc;

  .main-content {
    height: 100%;
    padding: 16px;
  }

  .left-panel {
    height: 100%;
    padding-right: 12px;

    .form-section {
      height: 100%;
      display: flex;
      flex-direction: column;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      overflow: hidden;

      .section-title {
        font-size: 20px;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 20px;
        padding: 16px 20px 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .inspection-form {
        flex-shrink: 0;

        // 表单分组标题样式
        .form-section-header {
          display: flex;
          align-items: center;
          margin: 20px 0 15px 0;
          padding: 8px 12px;
          background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
          border-left: 4px solid #0ea5e9;
          border-radius: 6px;

          .section-icon {
            font-size: 16px;
            margin-right: 8px;
          }

          .section-text {
            font-size: 14px;
            font-weight: 600;
            color: #0369a1;
          }
        }

        .form-row {
          // margin-bottom: 16px;
        }

        // 条码输入样式
        .barcode-item {
          .barcode-input {
            .el-input__inner {
              font-family: 'Courier New', monospace;
              font-weight: 600;
              letter-spacing: 1px;
            }
          }
        }

        // 机号显示样式
        .machine-item {
          .machine-display {
            display: flex;
            align-items: center;
            padding: 0px 14px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            // min-height: 40px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            white-space: nowrap; // 防止文本换行

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 3px;
              height: 100%;
              background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%);
              border-radius: 0 2px 2px 0;
            }

            &:hover {
              border-color: #cbd5e1;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              transform: translateY(-1px);
            }

            .machine-icon {
              color: #3b82f6;
              margin-right: 8px;
              margin-left: 6px;
              font-size: 16px;
              flex-shrink: 0; // 防止图标被压缩
              transition: all 0.3s ease;
              filter: drop-shadow(0 1px 2px rgba(59, 130, 246, 0.3));
            }

            .machine-text {
              color: #1e293b;
              font-size: 13px;
              font-weight: 500;
              flex: 1;
              min-width: 0; // 允许文本收缩
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;

              &:empty::before {
                content: '扫码获取';
                color: #94a3b8;
                font-style: italic;
                font-weight: 400;
              }
            }

            .status-icon {
              color: #10b981;
              font-size: 14px;
              margin-left: 6px;
              flex-shrink: 0; // 防止图标被压缩
              animation: fadeInScale 0.3s ease-out;
            }

            // 当有机号时的样式
            &.has-machine {
              background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
              border-color: #a7f3d0;

              &::before {
                background: linear-gradient(180deg, #10b981 0%, #059669 100%);
              }

              .machine-icon {
                color: #059669;
                filter: drop-shadow(0 1px 2px rgba(16, 185, 129, 0.3));
              }

              .machine-text {
                color: #064e3b;
                font-weight: 600;
                font-family: 'Courier New', monospace;
                letter-spacing: 0.5px;
              }
            }
          }
        }

        // 人员选择器样式
        .worker-selector {
          .worker-input {
            width: 100%;

            :deep(.el-select) {
              width: 100%;
            }

            :deep(.el-input__wrapper) {
              border-radius: 8px;
              transition: all 0.3s ease;

              &:hover {
                box-shadow: 0 2px 4px rgba(59, 130, 246, 0.15);
              }

              &.is-focus {
                box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
              }
            }
          }
        }

        // 重量信息样式
        .weight-row {
          .weight-item {
            .weight-label {
              display: flex;
              flex-direction: column;
              gap: 4px;

              .weight-tags {
                display: flex;
                gap: 4px;
              }
            }

            .weight-input {
              .el-input-number__increase,
              .el-input-number__decrease {
                background: #f1f5f9;
                border-color: #cbd5e1;

                &:hover {
                  background: #e2e8f0;
                }
              }

              .weight-unit {
                color: #64748b;
                font-weight: 500;
              }
            }
          }
        }
      }

      .info-display {
        flex: 1;
        margin-top: 20px;

        .info-section-header {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          padding: 8px 12px;
          background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
          border-left: 4px solid #f59e0b;
          border-radius: 6px;

          .section-icon {
            font-size: 16px;
            margin-right: 8px;
          }

          .section-text {
            font-size: 14px;
            font-weight: 600;
            color: #92400e;
          }
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;
          margin-bottom: 16px;
        }

        .info-card {
          background: #ffffff;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 12px;
          transition: all 0.3s ease;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          &:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            transform: translateY(-1px);
          }

          .info-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .info-icon {
              color: #6b7280;
              margin-right: 6px;
              font-size: 14px;
            }

            .info-label {
              font-size: 12px;
              color: #6b7280;
              font-weight: 500;
            }
          }

          .info-value {
            font-size: 14px;
            color: #111827;
            font-weight: 600;
            word-break: break-all;

            &.highlight {
              color: #059669;
              background: #ecfdf5;
              padding: 4px 8px;
              border-radius: 4px;
              display: inline-block;
            }
          }
        }

        .yarn-info-card {
          background: #ffffff;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 12px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          .yarn-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .yarn-icon {
              color: #6b7280;
              margin-right: 6px;
              font-size: 14px;
            }

            .yarn-label {
              font-size: 12px;
              color: #6b7280;
              font-weight: 500;
            }
          }

          .yarn-content {
            font-size: 14px;
            color: #111827;
            font-weight: 600;
            word-break: break-all;
            line-height: 1.5;
            background: #f9fafb;
            padding: 8px;
            border-radius: 4px;
            border: 1px dashed #d1d5db;
          }
        }
      }

      .bottom-actions {
        flex-shrink: 0;
        border-top: 1px solid #e4e7ed;
        padding: 16px 0;
        background: #fafbfc;
        border-radius: 0 0 8px 8px;

        .weight-requirement {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          padding: 8px 12px;
          background: #fff7ed;
          border: 1px solid #fed7aa;
          border-radius: 6px;

          .requirement-icon {
            color: #ea580c;
            margin-right: 8px;
            font-size: 16px;
          }

          .requirement-text {
            color: #9a3412;
            font-weight: 500;
            margin-right: 8px;
          }

          .requirement-value {
            color: #ea580c;
            font-weight: 600;
          }
        }

        .action-buttons {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .primary-actions {
            display: flex;
            gap: 12px;

            .save-btn {
              background: linear-gradient(135deg, #10b981 0%, #059669 100%);
              border: none;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(16, 185, 129, 0.25);
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(16, 185, 129, 0.35);
              }
            }

            .clear-btn {
              background: #f8fafc;
              border: 1px solid #e2e8f0;
              color: #64748b;
              border-radius: 8px;
              transition: all 0.3s ease;

              &:hover {
                background: #f1f5f9;
                border-color: #cbd5e1;
                color: #475569;
                transform: translateY(-1px);
              }
            }
          }

          .secondary-actions {
            display: flex;
            align-items: center;
            gap: 12px;

            .auto-save-checkbox {
              .checkbox-text {
                color: #64748b;
                font-weight: 500;
              }
            }

            .settings-btn {
              background: #f8fafc;
              border: 1px solid #e2e8f0;
              color: #64748b;
              border-radius: 8px;
              transition: all 0.3s ease;

              &:hover {
                background: #f1f5f9;
                border-color: #cbd5e1;
                color: #475569;
                transform: translateY(-1px);
              }
            }
          }
        }
      }
    }
  }

  .right-panel {
    height: 100%;
    padding-left: 12px;

    .defect-section {
      height: 100%;
      display: flex;
      flex-direction: column;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      overflow: hidden;

      .defect-tabs {
        flex: 1;
        display: flex;

        .defect-buttons {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          padding: 16px;
          background: #fafbfc;
          border-radius: 8px;
          border: 1px solid #e5e7eb;

          :deep(.el-button+.el-button){
            margin: 0
          }

          .defect-skeleton {
            display: flex;
            flex-wrap: wrap;
            width: 100%;
          }

          .defect-btn {
            min-width: 90px;
            height: 36px;
            border: 1px solid #e5e7eb;
            background: #ffffff;
            color: #6b7280;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

            &:hover {
              border-color: #3b82f6;
              color: #3b82f6;
              background: #eff6ff;
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(59, 130, 246, 0.15);
            }

            &.selected {
              background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
              border-color: #2563eb;
              color: white;
              box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
            }

            .defect-count {
              margin-left: 4px;
              font-weight: 600;
              background: rgba(255, 255, 255, 0.2);
              padding: 1px 4px;
              border-radius: 3px;
              font-size: 11px;
            }
          }
        }

        .tab-content {
          padding: 20px;
          text-align: center;
          color: #909399;
        }
      }

      .defect-statistics {
        flex-shrink: 0;
        border-top: 1px solid #e5e7eb;
        padding: 16px;
        background: #f8fafc;

        :deep(.el-scrollbar__wrap) {
          overflow-x: auto;
          overflow-y: hidden;
        }

        :deep(.el-scrollbar__view) {
          display: flex;
          align-items: center;
        }

        .statistics-content {
          display: flex;
          gap: 12px;
          padding-bottom: 8px;

          .stat-item {
            flex-shrink: 0;
            padding: 6px 12px;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            color: #374151;
            font-size: 13px;
            font-weight: 500;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

            &:hover {
              background: #eff6ff;
              border-color: #3b82f6;
              color: #3b82f6;
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(59, 130, 246, 0.15);
            }

            &.clickable {
              cursor: pointer;

              &:hover {
                background: #eff6ff;
                border-color: #3b82f6;
                color: #3b82f6;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
              }
            }

            &.no-data {
              background: #f9fafb;
              border-color: #e5e7eb;
              color: #9ca3af;
              font-style: italic;
              cursor: default;

              &:hover {
                background: #f9fafb;
                border-color: #e5e7eb;
                transform: none;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
              }
            }

            // 特殊样式：总疵点统计
            &:first-child {
              background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
              border-color: #f59e0b;
              color: #92400e;
              font-weight: 600;

              &:hover {
                background: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%);
                border-color: #d97706;
                color: #78350f;
              }
            }
          }
        }
      }
    }
  }

  // 弹窗样式
  .electronic-scale-modal {
    // padding: 20px;

    .stable-value-row {
      display: flex;
      align-items: center;
    }

    .hint-text {
      color: #909399;
      font-size: 12px;
    }

    .el-form-item {
      margin-bottom: 20px;

      .el-radio {
        margin-right: 15px;
      }
    }
  }

  // 美化提示样式
  .setting-label-container {
    display: flex;
    align-items: center;
    gap: 8px;

    .info-badge {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 18px;
      height: 18px;
      background: linear-gradient(135deg, #409eff, #66b1ff);
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
      }

      .info-icon {
        color: white;
        font-size: 12px;
      }
    }
  }

  .custom-tooltip-content {
    .tooltip-title {
      font-weight: 600;
      font-size: 14px;
      color: #fff;
      margin-bottom: 6px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      padding-bottom: 4px;
    }

    .tooltip-desc {
      font-size: 13px;
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.4;
    }
  }

  .stable-value-container {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .input-group {
      display: flex;
      align-items: center;
      gap: 12px;

      .stable-checkbox {
        flex-shrink: 0;
      }

      .stable-input {
        width: 200px;

        .input-suffix {
          color: #909399;
          font-size: 12px;
        }
      }
    }

    .setting-hint {
      display: flex;
      align-items: flex-start;
      gap: 6px;
      padding: 8px 12px;
      background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
      border: 1px solid #bae6fd;
      border-radius: 6px;

      .hint-icon {
        color: #0ea5e9;
        font-size: 14px;
        margin-top: 1px;
        flex-shrink: 0;
      }

      .hint-text {
        font-size: 12px;
        color: #0369a1;
        line-height: 1.4;
      }
    }
  }

  .data-setting-container {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .data-input {
      width: 300px;
    }

    .setting-note {
      font-size: 12px;
      color: #909399;
      padding-left: 4px;
    }
  }

}
// 重量限制标签样式
.weight-limit-tag {
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  // 自定义成功标签样式
  &.el-tag--success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-color: #10b981;
    color: white;
  }

  // 自定义危险标签样式
  &.el-tag--danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-color: #ef4444;
    color: white;

  }
}
// 全局样式调整
:deep(.el-input-number) {
  width: 100%;

  .el-input__inner {
    text-align: left;
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .grey-cloth-inspection {
    .main-content {
      padding: 12px;
    }

    .left-panel, .right-panel {
      padding-left: 8px;
      padding-right: 8px;
    }

    .info-grid {
      grid-template-columns: 1fr;
    }
  }
}

// 加载状态样式
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}
</style>
